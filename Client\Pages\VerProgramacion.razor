@page "/VerProgramacion"
@using System.Security.Claims
@using System.Text.Json
@using System.ComponentModel.DataAnnotations
@using System.Reflection
@using DevExpress.Blazor.Internal
@using ProgramadorGeneralBLZ.Shared.DTO
@using Microsoft.AspNetCore.SignalR.Client
@using ProgramadorGeneralBLZ.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.SignalR
@using ProgramadorGeneralBLZ.Shared.ResponseModels
@using ProgramadorGeneralBLZ.Client.Pages.Components

@inject SpinnerService SpinnerService
@inject HttpClient Http
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage
@inject IToastService ToastService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthState
@inject IJSRuntime JsRuntime

@attribute [Authorize]
@implements IAsyncDisposable

<PageTitle>PROGRAMACION @(CurrentMaquina?.IdmaquinaG21 ?? "DIGITAL")</PageTitle>

<AuthorizeView>
	<Authorized Context="authContext">
		<DxLayoutBreakpoint DeviceSize="DeviceSize.Large" />
		<div class="h-100 px-2 py-1">
			<DxFormLayout CssClass="">
				<!-- Header fijo -->
				<div class="header-container">
					<DxFormLayoutGroup>
						<HeaderTemplate>
						</HeaderTemplate>
						<Items>
							<DxFormLayoutItem ColSpanLg="4">
								<span class="cabecera-reporte">
									<h2>PROGRAMACION @TipoProgramacion</h2>
								</span>
							</DxFormLayoutItem>

							<DxFormLayoutGroup ColSpanLg="4" Decoration="FormLayoutGroupDecoration.None">
								@if (_listaRoles != null && EsResponsable)
								{
									<DxFormLayoutItem Caption="Máquina:" Context="ddMaquina" ColSpanLg="6" CaptionCssClass="cabecera-reporte">
										<DxComboBox Data="@DatosMaquinas"
													TextFieldName="@nameof(MaquinaDTO.IdmaquinaG21)"
													@bind-Value="@CurrentMaquina"
													@bind-Value:after="async () => await OnUpdateMaquina()"
													NullText="Máquina...">
										</DxComboBox>
									</DxFormLayoutItem>
								}
								else
								{
									<DxFormLayoutItem ColSpanLg="6">
										<span class="cabecera-reporte">
											<h2>MAQUINA: @(CurrentMaquina?.IdmaquinaG21 ?? string.Empty)</h2>
										</span>
									</DxFormLayoutItem>
								}

							</DxFormLayoutGroup>
							<DxFormLayoutItem ColSpanLg="4">
								<span class="cabecera-reporte" style="text-align: end">
									<h2><FechaHora></FechaHora></h2>
								</span>
							</DxFormLayoutItem>
						</Items>
					</DxFormLayoutGroup>
				</div>
				<!-- Contenido scrollable -->
				<div class="content-scroll @(_listaPedidos is { Count: > 0 } ? "" : "no-content")">
					@if (_listaPedidos is { Count: > 0 })
					{
						{
							var esBarnizadoReportV2 = _listaPedidos.First().NombreReporte.Equals("BarnizadoReportV2");

							if (esBarnizadoReportV2)
							{
								// Para BarnizadoReportV2: agrupar por ApliProducto y Orden
								foreach (var grupo in _listaPedidos
									         .OrderBy(o => o.Posicion)
									         .GroupBy(item => new { item.ApliProducto, item.Orden }))
								{
									var tipoPedido = grupo.FirstOrDefault()?.TipoPedido ?? string.Empty;
									switch (tipoPedido)
									{
										case "Barnizado":
											<VerProgramacionBarnizado Cabecera="@grupo.Key.ApliProducto"
											                          ListaPedidos="@grupo.ToList()"
											                          OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
											                          OnShowPopup="HandleShowPopupObservaciones"
											                          EsJefeTurno="EsJefeTurno"
											                          EsEncargado="EsEncargado"
											                          EsRodillos="EsRodillos"
											                          EsMaquina="EsMaquina" />
											break;
										default:
											<VerProgramacionLitografia Cabecera="@grupo.Key.ApliProducto"
											                           ListaPedidos="@grupo.ToList()"
											                           OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
											                           OnShowPopup="HandleShowPopupObservaciones"
											                           EsJefeTurno="EsJefeTurno"
											                           EsEncargado="EsEncargado"
											                           EsRodillos="EsRodillos"
											                           EsMaquina="EsMaquina" />
											break;
									}
								}
							}
							else
							{
								// Para otros reportes: procesar items individuales (agrupados por Posicion)
								foreach (var grupo in _listaPedidos
									         .OrderBy(o => o.Posicion)
									         .GroupBy(item => new { item.Posicion }))
								{
									foreach (var item in grupo)
									{
										var tipoPedido = item.TipoPedido ?? string.Empty;
										switch (tipoPedido)
										{
											case "Barnizado":
												<VerProgramacionBarnizado Cabecera="@item.ApliProducto"
												                          ListaPedidos="@(new List<PedidoProgramacionEnPantallaDTO> { item })"
												                          OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
												                          OnShowPopup="HandleShowPopupObservaciones"
												                          EsJefeTurno="EsJefeTurno"
												                          EsEncargado="EsEncargado"
												                          EsRodillos="EsRodillos"
												                          EsMaquina="EsMaquina" />
												break;
											default:
												<VerProgramacionLitografia Cabecera="@item.ApliProducto"
												                           ListaPedidos="@(new List<PedidoProgramacionEnPantallaDTO> { item })"
												                           OnEstadoPedidoUpdatedViaHub="HandleEstadoPedidoUpdatedViaHub"
												                           OnShowPopup="HandleShowPopupObservaciones"
												                           EsJefeTurno="EsJefeTurno"
												                           EsEncargado="EsEncargado"
												                           EsRodillos="EsRodillos"
												                           EsMaquina="EsMaquina" />
												break;
										}
									}
								}
							}
						}
					}
				</div>
			</DxFormLayout>
		</div>

		@* POPUP PARA MENSAJES DEL HUB *@
		<DxPopup @bind-Visible="PopupMensajeHubVisible"
				 ShowFooter="true"
				 CloseOnEscape="false"
				 CloseOnOutsideClick="false"
				 HeaderText="AVISO"
				 Width="700px">
			<BodyContentTemplate>
				<div class="d-flex p-2">
					<div class="me-3">
						<i class="@ClaseIconoMensajeHub" style="font-size: 24px;"></i>
					</div>
					<div class="flex-grow-1">
						@if (UltimoMensajeHub != null)
						{
							<span class="text-muted small">
								[@(UltimoMensajeHub.Fecha.ToString("dd/MM/yyyy HH:mm:ss"))]
							</span>
							<div class="mt-1">
								<span class="text-primary fw-bold">@(UltimoMensajeHub.IdUsuario):</span>
								<span> @(UltimoMensajeHub.Mensaje)</span>
							</div>
							@if (!string.IsNullOrEmpty(UltimoMensajeHub.Extra))
							{
								<div class="mt-2 text-muted small">
									@(UltimoMensajeHub.Extra)
								</div>
							}
						}
					</div>
				</div>
			</BodyContentTemplate>
			<FooterContentTemplate>
				<DxButton Click="@(() => PopupMensajeHubVisible = false)"
						  RenderStyle="ButtonRenderStyle.Success"
						  CssClass="float-end me-2"
						  Text="OK" />
			</FooterContentTemplate>
		</DxPopup>

		<DxPopup @bind-Visible="@PopupObservacionVisible"
				 ShowFooter="true"
				 CloseOnEscape="true"
				 CloseOnOutsideClick="false"
				 Shown="@SetFocus"
				 Closed="@LimpiarCamposPopUpObservaciones"
				 HeaderText=@($"Añadir {TextoHeaderPopup}")>
			<BodyContentTemplate>
				<DxMemo @bind-Text="PopUpTextValue" Rows="5" />
			</BodyContentTemplate>
			<FooterContentTemplate Context="footer">
				<button onclick="@(() => ProcesarPopUpObservaciones(true))" class="btn btn-success me-2" type="button">Guardar</button>
				<button onclick="@(() => ProcesarPopUpObservaciones(false))" class="btn btn-danger" type="button">Cancelar</button>
			</FooterContentTemplate>
		</DxPopup>

		@* DIV ORIGINAL FLOTANTE PARA MENSAJES DEL HUB *@
		<div class="position-absolute bottom-0 end-0 m-3" style="z-index: 50">
			<div class="card shadow-lg" style="width: 450px; max-height: 500px;">
				<div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
					<h6 class="mb-0">
						<i class="bi bi-chat-dots me-2"></i>
						Mensajes
					</h6>
					<div class="d-flex align-items-center">
						<button class="btn btn-sm btn-outline-light" @onclick="ToggleMensajesHub">
							<i class="bi @(_mostrarMensajesHub ? "bi-chevron-down" : "bi-chevron-up")"></i>
						</button>
					</div>
				</div>

				@if (_mostrarMensajesHub)
				{
					@* Filtros por tipo de cambio *@
					<div class="card-body border-bottom p-2" style="position: relative;">
						<div class="small text-muted mb-2">Filtrar por tipo:</div>
						<DxTagBox Data="@_tiposCambioDisponibles"
								  @bind-Values="@_tiposCambioSeleccionados"
								  NullText="Todos los tipos"
								  ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
								  DropDownDirection="DropDownDirection.Up"
								  SizeMode="SizeMode.Small">
							<ItemDisplayTemplate Context="itemContext">
								@(((Enums.ProgramacionesPantalla_TiposLog)itemContext.DataItem).GetAttribute<DisplayAttribute>().Name)
							</ItemDisplayTemplate>
							<TagDisplayTemplate Context="tagContext">
								<span class="badge @GetBadgeClassForTipo((Enums.ProgramacionesPantalla_TiposLog)tagContext.DataItem) me-1">
									@(((Enums.ProgramacionesPantalla_TiposLog)tagContext.DataItem).GetAttribute<DisplayAttribute>().Name)
									<button type="button" class="btn-close btn-close-white ms-1" style="font-size: 0.6em;" @onclick="tagContext.RemoveTagAction"></button>
								</span>
							</TagDisplayTemplate>
						</DxTagBox>
					</div>

					@* Lista de mensajes *@
					<div class="card-body p-0" style="max-height: 300px; min-height: 300px; overflow-y: auto;">
						@if (_todosMensajes.Count == 0)
						{
							<div class="p-3 text-muted text-center">
								<i class="bi bi-inbox"></i>
								<br />
								@if (_cargandoMensajes)
								{
									<span>Cargando mensajes...</span>
								}
								else if (_tiposCambioSeleccionados.Any())
								{
									<span>No hay mensajes del tipo seleccionado</span>
								}
								else
								{
									<span>No hay mensajes</span>
								}
							</div>
						}
						else
						{
							@foreach (var mensaje in _todosMensajes)
							{
								<div class="border-bottom p-2">
									<div class="d-flex justify-content-between align-items-start">
										<div class="flex-grow-1">
											<div class="d-flex align-items-center mb-1">
												<small class="text-muted me-2">@mensaje.Fecha.ToString("dd/MM/yyyy HH:mm:ss") - @mensaje.IdUsuario</small>
												@{
													var codLog = (int)mensaje.IdTipoLog;
												}
												<span class="badge @GetBadgeClassForTipo((Enums.ProgramacionesPantalla_TiposLog)mensaje.IdTipoLog) small">
													@(codLog != 0 ? ((Enums.ProgramacionesPantalla_TiposLog)mensaje.IdTipoLog).GetAttribute<DisplayAttribute>().Name : "")
												</span>
											</div>
											<div class="small">@mensaje.Mensaje</div>
											@if (!string.IsNullOrEmpty(mensaje.Extra))
											{
												<div class="small text-muted">@mensaje.Extra</div>
											}
										</div>
										<i class="bi bi-chat text-secondary ms-2"></i>
									</div>
								</div>
							}
						}
					</div>

					@* Botón cargar más mensajes *@
					<div class="text-center p-2">
						<DxButton Enabled="!_cargandoMensajes"
						          Visible="_hayMasMensajes && _todosMensajes.Count > 0"
						          Click="CargarMasMensajes"
						          SizeMode="SizeMode.Small"
						          RenderStyle="ButtonRenderStyle.Primary">
							<div class="d-flex align-items-center">
								<DxWaitIndicator Visible="_cargandoMensajes"/>
								<span class="mx-2">@(_cargandoMensajes ? "Cargando mensajes..." : "Cargar más mensajes")</span>
							</div>
						</DxButton>
					</div>
				}
			</div>
		</div>

	</Authorized>
	<NotAuthorized>
		<NoPuedesPasar />
	</NotAuthorized>
</AuthorizeView>

@code {
	private HubConnection _hubConnection;
	private List<PedidoProgramacionEnPantallaDTO> _listaPedidos { get; set; }
	private int FiltroMaquina { get; set; }
	private Timer _timer;
	private List<string> _listaRoles = [];
	private string TipoProgramacion { get; set; }
	private MaquinaDTO? CurrentMaquina { get; set; }
	private ListResult<MaquinaDTO> ResultMaquinas { get; set; }
	private List<MaquinaDTO> DatosMaquinas { get; set; }
	private bool EsResponsable { get; set; } = false;

	// Propiedades para popup de mensajes del hub
	private bool PopupMensajeHubVisible { get; set; } = false;
	private ProgramacionesPantallaLogDTO UltimoMensajeHub { get; set; }
	private string ClaseIconoMensajeHub { get; set; } = string.Empty;




	// Propiedades comunes para popup de observaciones
	private ClaimsPrincipal User { get; set; }
	private bool EsJefeTurno { get; set; }
	private bool EsEncargado { get; set; }
	private bool EsRodillos { get; set; }
	private bool EsMaquina { get; set; }
	private bool PopupObservacionVisible { get; set; }
	private PedidoProgramacionEnPantallaDTO? PedidoPopup;
	private string TextoHeaderPopup { get; set; }
	private string PopUpTextValue { get; set; } = string.Empty;
	DxMemo _component;

	// Propiedades para el elemento flotante de mensajes del hub
	private List<ProgramacionesPantallaLogDTO> _mensajesHub = new();
	private bool _mostrarMensajesHub;

	// Propiedades para lazy loading de mensajes históricos
	private List<ProgramacionesPantallaLogDTO> _mensajesHistoricos = new();
	private List<ProgramacionesPantallaLogDTO> _todosMensajes = new();
	private const int _tamanoPagina = 10;
	private bool _cargandoMensajes = false;
	private bool _hayMasMensajes = true;

	// Propiedades para filtrado por tipo de cambio
	private IEnumerable<Enums.ProgramacionesPantalla_TiposLog> _tiposCambioDisponibles =
		Enum.GetValues<Enums.ProgramacionesPantalla_TiposLog>().OrderBy(i => i.GetAttribute<DisplayAttribute>().Name);

	private IEnumerable<Enums.ProgramacionesPantalla_TiposLog> _tiposCambioSeleccionadosInternal =
		new List<Enums.ProgramacionesPantalla_TiposLog>();

	private IEnumerable<Enums.ProgramacionesPantalla_TiposLog> _tiposCambioSeleccionados
	{
		get => _tiposCambioSeleccionadosInternal;
		set
		{
			_tiposCambioSeleccionadosInternal = value;
			FiltrarMensajes();
		}
	}

	protected override async Task OnInitializedAsync()
	{
		SpinnerService.Show();

		ResultMaquinas = await Http.GetFromJsonAsync<ListResult<MaquinaDTO>>($"Maquinas/dropdownbytipo/{Enums.TipoMaquina.Todas}");
		var listaErrores = ResultMaquinas.Errors.ToList();
		if (listaErrores.Any())
		{
			foreach (var error in listaErrores)
			{
				ToastService.ShowError($"{error}");
			}
		}
		else
		{
			// Parametrizamos HUB
			_hubConnection = new HubConnectionBuilder()
				.WithUrl(Navigation.ToAbsoluteUri("/imprimirHub"))
				.WithAutomaticReconnect(new[] { 
					TimeSpan.Zero, // 1er intento de reconexión a los 0 segs
					TimeSpan.FromSeconds(2), // 2o intento de reconexión a los 2 segs 
					TimeSpan.FromSeconds(5), // 3o intento de reconexión a los 5 segs
					TimeSpan.FromSeconds(10) // 40 intento de reconexión a los 10 segs
				})
				.Build();

			_hubConnection.Closed += async (ex) =>
			{
				// mensaje de error en caso de desconexión. sale tras los reconnecting fallidos
				var mensaje = new ProgramacionesPantallaLogDTO()
				{
					IdUsuario = "Sistema",
					Mensaje = $"Se ha desconectado del servidor de mensajes. Recarga la página.\n{ex.Message}",
					Fecha = DateTime.Now
				};
				AniadirMensajeHub(mensaje);
				ShowPopupMensajeHub(mensaje, true);
				StateHasChanged();

				await Task.CompletedTask;
			};

			_hubConnection.Reconnecting += async (e) =>
			{
				// si se reconecta después de perder conexión, simulamos postcarga
				SpinnerService.Show();
				ToastService.ShowError("Reconectando con el servidor de mensajes");
				StateHasChanged();
				await Task.CompletedTask;
			};

			_hubConnection.Reconnected += async (e) =>
			{
				// si se reconecta después de perder conexión, simulamos postcarga
				SpinnerService.Show();
				StateHasChanged();
				await CargaPostConexionHub();
				await Task.CompletedTask;
				StateHasChanged();
			};

			_hubConnection.On<SingleResultHub<int>>(TiposMensajesImprimirHub.GrupoAbandonado, async (result) =>
			{
				try
				{
					if (GetEstadoConexionHub())
						await _hubConnection.InvokeAsync("SendUnirseAGrupo", User.Identity?.Name, CurrentMaquina.Idmaquina);
				}
				catch (Exception ex)
				{
					// Mostrar el mensaje de error del servidor
					ToastService.ShowError(ex.Message);
					ShowPopupMensajeHub(new ProgramacionesPantallaLogDTO()
					{
						IdUsuario = "Sistema",
						Mensaje = ex.Message,
						Fecha = DateTime.Now,
						Extra = "Error en SendUnirseAGrupo"
					}, true);
					StateHasChanged();
				}
			});
			_hubConnection.On<SingleResultHub<int>>(TiposMensajesImprimirHub.UnidoANuevoGrupo, async (result) =>
			{
				await LocalStorage.SetItemAsync("maquinaId", CurrentMaquina.Idmaquina);
				FiltroMaquina = CurrentMaquina.Idmaquina;
				TipoProgramacion = CurrentMaquina.TipoMaquina == "Barnizadora" ? "BARNIZADO" : "LITOGRAFIA";
				LimpiarMensajesHub();
				_mostrarMensajesHub = false; // mantenemos cerrado el listado de mensajesHub
				await CargarMensajesHistoricos();
				await EstablecerIdLinea();
			});
			_hubConnection.On<ListResultHub<PedidoProgramacionEnPantallaDTO>>(TiposMensajesImprimirHub.IdLineaActualizada_Emisor, (result) =>
			{
				FuncionRecepcionDatos_Emisor(result);
			});
			_hubConnection.On<ListResultHub<PedidoProgramacionEnPantallaDTO>>(TiposMensajesImprimirHub.ProgramacionActualizada_Receptores, (result) =>
			{
				FuncionRecepcionDatos_Receptores(result);
			});
			_hubConnection.On<ListResultHub<PedidoProgramacionEnPantallaDTO>>(TiposMensajesImprimirHub.PedidoActualizado_Receptores, (result) =>
			{
				FuncionRecepcionDatos_Receptores(result);
			});
			_hubConnection.On<SingleResultHub<PedidoProgramacionEnPantallaDTO>>(TiposMensajesImprimirHub.PedidoActualizado_Emisor, (result) =>
			{
				LimpiarCamposPopUpObservaciones();
				FuncionDtoActualizado_Emisor(result);
			});
			_hubConnection.On<SingleResultHub<int>>(TiposMensajesImprimirHub.PedidoPublicado_Emisor, (result) =>
			{
				FuncionDtoPublicado_Emisor(result);
			});

			// Damos valor a props
			var authstate = await AuthState.GetAuthenticationStateAsync();
			User = authstate.User;
			_listaRoles = User.Claims.Where(claim => claim.Type == "extension_Roles").Select(claim => claim.Value).ToList();
			EsResponsable = _listaRoles.Any(o => o is Roles.Admin or Roles.Programador or Roles.JefeTurno or Roles.Encargado or Roles.Rodillos);

			// Configurar roles para componentes hijos
			EsJefeTurno = _listaRoles.Any(o => o == Roles.JefeTurno);
			EsEncargado = _listaRoles.Any(o => o == Roles.Encargado);
			EsRodillos = _listaRoles.Any(o => o == Roles.Rodillos);
			EsMaquina = _listaRoles.Count() == 1 && _listaRoles.Any(o => o is Roles.B3 or Roles.M4);

			DatosMaquinas = ResultMaquinas.Data;
			DatosMaquinas = DatosMaquinas == null ? DatosMaquinas : DatosMaquinas.OrderBy(i => i.Nombremaquina).ToList();
			if (EsResponsable)
			{
				var maquinaId = await LocalStorage.GetItemAsync<int?>("maquinaId");
				CurrentMaquina = DatosMaquinas.FirstOrDefault(o => o.Idmaquina == maquinaId) ?? DatosMaquinas.First();

			}
			else
			{
				CurrentMaquina = DatosMaquinas.FirstOrDefault(o => o.IdmaquinaG21.Replace("-", "").Replace("_", "").Replace(" ", "") == _listaRoles.First());
			}
			FiltroMaquina = CurrentMaquina?.Idmaquina ?? 0;
			TipoProgramacion = CurrentMaquina?.TipoMaquina == "Barnizadora" ? "BARNIZADO" : "LITOGRAFIA";

			await _hubConnection.StartAsync();
			await CargaPostConexionHub();
		}
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender)
		{
			await JsRuntime.InvokeVoidAsync("stopInactivityTimer");
		}
	}

	private async Task CargaPostConexionHub()
	{
		await OnUpdateMaquina(); // no sería necesario abandonar grupo porque no tiene, pero como naturalmente luego llama unirse a uno nuevo, lo dejamos así

		// Cargar mensajes históricos iniciales
		if (CurrentMaquina != null)
			await CargarMensajesHistoricos();
	}

	private void FuncionRecepcionDatos_Emisor(ListResultHub<PedidoProgramacionEnPantallaDTO> result)
	{
		SpinnerService.Show();
		_listaPedidos = result.Data.ToList();

		// Procesar mensajes del hub
		ProcesarMensajesHub(result);
		if (result.HasErrors)
			ShowPopupMensajeHub(result.Errors.FirstOrDefault(), true);

		StateHasChanged();
		SpinnerService.Hide();
	}

	private void FuncionRecepcionDatos_Receptores(ListResultHub<PedidoProgramacionEnPantallaDTO> result)
	{
		SpinnerService.Show();
		if (result.Data != null)
			_listaPedidos = result.Data.ToList();

		// Procesar mensajes del hub. Si se detecta errores o info, sacamos el popup informando
		// Info sólo se informa a receptores
		ProcesarMensajesHub(result);
		if (result.HasErrors)
			ShowPopupMensajeHub(result.Errors.FirstOrDefault(), true);
		else if (result.HasInfo)
			ShowPopupMensajeHub(result.Info.FirstOrDefault(), false);

		StateHasChanged();
		SpinnerService.Hide();
	}

	private void FuncionDtoActualizado_Emisor(SingleResultHub<PedidoProgramacionEnPantallaDTO> result)
	{
		// Si hay datos, actualizar en pantalla
		if (result.Data != null)
			UpdateDtoEnPantalla(result.Data);

		// Procesar mensajes del hub
		ProcesarMensajesHub(result);
		if (result.HasErrors)
			ShowPopupMensajeHub(result.Errors.FirstOrDefault(), true);

		StateHasChanged();
		SpinnerService.Hide();
	}

	private void FuncionDtoPublicado_Emisor(SingleResultHub<int> result)
	{
		ProcesarMensajesHub(result);
		if (result.HasErrors)
			ShowPopupMensajeHub(result.Errors.FirstOrDefault(), true);
	}



	private void UpdateDtoEnPantalla(PedidoProgramacionEnPantallaDTO dto)
	{
		// Evento para respuesta inmediata de cambio de estado en local
		// Indica al cliente que actualiza el estado, que el cambio se ha hecho en bbdd
		// creo que vale asi. no haría falta
		var index = _listaPedidos?.FindIndex(m =>
			m.Idpedido == dto.Idpedido
			&& m.Idaplicacion == dto.Idaplicacion
			&& m.Idlinea == dto.Idlinea
			&& m.Posicion == dto.Posicion);

		if (index.HasValue && index >= 0 && _listaPedidos != null)
			_listaPedidos[index.Value] = dto;
	}

	public async ValueTask DisposeAsync()
	{
		if (_hubConnection is not null)
		{
			await _hubConnection.DisposeAsync();
		}
		_timer?.DisposeAsync();
		await JsRuntime.InvokeVoidAsync("startInactivityTimer", true, DotNetObjectReference.Create(this));

	}

	private async Task OnUpdateMaquina()
	{
		// SpinnerService.Show();
		// await LocalStorage.SetItemAsync("maquinaId", CurrentMaquina.Idmaquina);
		// FiltroMaquina = CurrentMaquina.Idmaquina;
		// TipoProgramacion = CurrentMaquina.TipoMaquina == "Barnizadora" ? "BARNIZADO" : "LITOGRAFIA";
		// LimpiarMensajesHub();
		// await EstablecerIdLinea();

		try
		{
			if (GetEstadoConexionHub())
			{
				SpinnerService.Show();
				await _hubConnection.InvokeAsync("SendAbandonarGrupo", User.Identity?.Name);
			}
		}
		catch (Exception ex)
		{
			// Mostrar el mensaje de error del servidor
			ToastService.ShowError(ex.Message);
			ShowPopupMensajeHub(new ProgramacionesPantallaLogDTO()
			{
				IdUsuario = "Sistema",
				Mensaje = ex.Message,
				Fecha = DateTime.Now,
				Extra = "Error en SendAbandonarGrupo"
			}, true);
			StateHasChanged();
		}
	}

	private async Task EstablecerIdLinea()
	{
		try
		{
			if (GetEstadoConexionHub())
			{
				SpinnerService.Show();
				await _hubConnection.InvokeAsync("SendIdLinea", FiltroMaquina, User.Identity?.Name);
			}
		}
		catch (Exception ex)
		{
			// Mostrar el mensaje de error del servidor
			ToastService.ShowError(ex.Message);
			ShowPopupMensajeHub(new ProgramacionesPantallaLogDTO()
			{
				IdUsuario = "Sistema",
				Mensaje = ex.Message,
				Fecha = DateTime.Now,
				Extra = "Error en SendIdLinea"
			}, true);
			StateHasChanged();
		}
	}

	private async Task HandleEstadoPedidoUpdatedViaHub(PedidoProgramacionEnPantallaDTO dto, Enums.ProgramacionesPantalla_EstadosPedido nuevoEstado)
	{
		try
		{
			if (GetEstadoConexionHub())
			{
				SpinnerService.Show();
				await _hubConnection.InvokeAsync("SendUpdatePedido", dto, nuevoEstado, null, null, FiltroMaquina, User.Identity?.Name);
			}
		}
		catch (Exception ex)
		{
			// Mostrar el mensaje de error del servidor
			ToastService.ShowError(ex.Message);
			ShowPopupMensajeHub(new ProgramacionesPantallaLogDTO()
			{
				IdUsuario = "Sistema",
				Mensaje = ex.Message,
				Fecha = DateTime.Now,
				Extra = "Error en SendUpdatePedido"
			}, true);
			StateHasChanged();
		}
	}

	// Métodos comunes extraídos de los componentes hijos
	private Task HandleShowPopupObservaciones(PedidoProgramacionEnPantallaDTO dto, string texto)
	{
		string valorActual = string.Empty;
		switch (texto)
		{
			case "Rodillo":
				valorActual = dto.Rodillo ?? string.Empty;
				break;
			case "Nota":
				valorActual = dto.NotaJefeTurno ?? string.Empty;
				break;
		}

		PedidoPopup = dto;
		TextoHeaderPopup = texto;
		PopupObservacionVisible = true;
		PopUpTextValue = valorActual;
		StateHasChanged();
		return Task.CompletedTask;
	}

	private async Task ProcesarPopUpObservaciones(bool guardar)
	{
		if (guardar)
		{
			try
			{
				if (GetEstadoConexionHub())
				{
					SpinnerService.Show();
					var campo = EsEncargado || EsJefeTurno ? "NotaJefeTurno" : "Rodillo";
					await _hubConnection.InvokeAsync("SendUpdatePedido", PedidoPopup, null, campo, PopUpTextValue, FiltroMaquina, User.Identity?.Name);
				}
			}
			catch (Exception ex)
			{
				// Mostrar el mensaje de error del servidor
				ToastService.ShowError(ex.Message);
				ShowPopupMensajeHub(new ProgramacionesPantallaLogDTO()
				{
					IdUsuario = "Sistema",
					Mensaje = ex.Message,
					Fecha = DateTime.Now,
					Extra = "Error en SendUpdatePedido"
				}, true);
				StateHasChanged();
			}
		}
		else
		{
			LimpiarCamposPopUpObservaciones();
		}
	}

	private void LimpiarCamposPopUpObservaciones()
	{
		PopupObservacionVisible = false;
		PedidoPopup = null;
		TextoHeaderPopup = string.Empty;
		PopUpTextValue = string.Empty;
	}

	private async Task SetFocus()
	{
		if (_component != null)
			await _component.FocusAsync();
	}

	// Métodos para el elemento flotante de mensajes del hub
	private void AniadirMensajeHub(ProgramacionesPantallaLogDTO mensaje)
	{
		_mostrarMensajesHub = true;
		_mensajesHub.Insert(0, mensaje);

		// Actualizar lista filtrada
		FiltrarMensajes();
		StateHasChanged();
	}

	private void ToggleMensajesHub()
	{
		_mostrarMensajesHub = !_mostrarMensajesHub;
		StateHasChanged();
	}



	private void ShowPopupMensajeHub(ProgramacionesPantallaLogDTO mensaje, bool esError)
	{
		UltimoMensajeHub = mensaje;
		PopupMensajeHubVisible = true;
		ClaseIconoMensajeHub = esError ? "bi bi-exclamation-triangle-fill text-danger" : "bi bi-check-circle-fill text-success";
	}

	private void LimpiarMensajesHub()
	{
		_mensajesHub.Clear();
		_mensajesHistoricos.Clear();
		_todosMensajes.Clear();
		_hayMasMensajes = true;
		StateHasChanged();
	}

	private void FiltrarMensajes()
	{
		ActualizarListaCombinada();
		StateHasChanged();
	}

	// Métodos para lazy loading de mensajes históricos
	private async Task CargarMensajesHistoricos()
	{
		if (_cargandoMensajes || CurrentMaquina == null) return;

		_cargandoMensajes = true;
		StateHasChanged();

		try
		{
			// Obtener el ID más pequeño de los mensajes históricos actuales
			long? idMenorQue = _mensajesHistoricos.Count > 0 ? _mensajesHistoricos.Min(m => m.Id) : null;

			var url = $"DatosGenerales/mensajeslog?idLinea={CurrentMaquina.Idmaquina}&tamanoPagina={_tamanoPagina}";
			if (idMenorQue.HasValue)
				url += $"&idMenorQue={idMenorQue.Value}";

			var response = await Http.GetFromJsonAsync<ListResult<ProgramacionesPantallaLogDTO>>(url);
			var listaErrores = response?.Errors ?? new List<string>();

			if (listaErrores.Any())
			{
				ToastService.ShowError($"Error al cargar mensajes: {listaErrores.First()}");
			}
			else if (response?.Data != null)
			{
				if (response.Data.Count < _tamanoPagina)
					_hayMasMensajes = false;

				_mensajesHistoricos.AddRange(response.Data);
				ActualizarListaCombinada();
			}
		}
		catch (Exception ex)
		{
			ToastService.ShowError($"Error al cargar mensajes: {ex.Message}");
		}
		finally
		{
			_cargandoMensajes = false;
			StateHasChanged();
		}
	}

	private async Task CargarMasMensajes()
	{
		if (_cargandoMensajes || !_hayMasMensajes) return;

		await CargarMensajesHistoricos();
	}

	private void ActualizarListaCombinada()
	{
		// Filtrar mensajes del hub y históricos según tipos seleccionados
		var mensajesHub = _tiposCambioSeleccionados.Any()
			? _mensajesHub.Where(m => _tiposCambioSeleccionados.Contains((Enums.ProgramacionesPantalla_TiposLog)m.IdTipoLog))
			: _mensajesHub.AsEnumerable();

		var mensajesHistoricos = _tiposCambioSeleccionados.Any()
			? _mensajesHistoricos.Where(m => _tiposCambioSeleccionados.Contains((Enums.ProgramacionesPantalla_TiposLog)m.IdTipoLog))
			: _mensajesHistoricos.AsEnumerable();

		_todosMensajes = mensajesHub.Concat(mensajesHistoricos)
			.OrderByDescending(m => m.Fecha)
			.ToList();
	}

	private void ProcesarMensajesHub(PrimitiveResultHub result)
	{
		if (result.HasErrors)
			foreach (var mensaje in result.Errors)
				AniadirMensajeHub(mensaje);
		else if (result.HasInfo)
			foreach (var mensaje in result.Info)
				AniadirMensajeHub(mensaje);
	}

	private string GetBadgeClassForTipo(Enums.ProgramacionesPantalla_TiposLog tipoCambio)
	{
		return tipoCambio switch
		{
			Enums.ProgramacionesPantalla_TiposLog.ProgramacionActualizada => "bg-primary",
			Enums.ProgramacionesPantalla_TiposLog.CambioEstado => "bg-warning",
			Enums.ProgramacionesPantalla_TiposLog.ActualizacionRodillo => "bg-info",
			Enums.ProgramacionesPantalla_TiposLog.ActualizacionNota => "bg-success",
			_ => "bg-secondary"
		};
	}

	private bool GetEstadoConexionHub()
	{
		var estadoOk = _hubConnection?.State == HubConnectionState.Connected;
		if (!estadoOk)
		{
			var mensaje = new ProgramacionesPantallaLogDTO()
			{
				IdUsuario = "Sistema",
				Mensaje = "No estás conectado al servidor de mensajes. Recarga la página y prueba de nuevo.",
				Fecha = DateTime.Now,
				Extra = ""
			};
			ShowPopupMensajeHub(mensaje, true);
			AniadirMensajeHub(mensaje);
		}

		return estadoOk;
	}
}